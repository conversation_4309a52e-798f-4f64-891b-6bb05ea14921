import {
  <PERSON>H<PERSON>ler,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  Alert,
  Dimensions,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import React, { useCallback, useEffect, useState, useMemo } from 'react';
import {
  useTheme,
  Switch,
  Modal,
  Portal,
  IconButton,
  Appbar,
  TouchableRipple,
  List,
  Divider,
  Badge,
  Button,
  Text as PaperText
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import BuyNowModal from './components/BuyNowModal';
import { usePreferencesContext } from './store/PreferencesContext';
import apiClient from './services/ApiClient';
import {
  GoogleSignin,
  GoogleSigninButton,
} from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { usePurchase } from './store/PurchaseContext';

import AuthService from './services/AuthService';

// Import contexts
import { useLogin } from './store/LoginContext';
import { useExamContext } from './store/ExamContext';
import { useQnAContext } from './store/QnAContext';

// Import utilities
import { performLogout } from './utils/loginUtils';

// Import device info for getting app version
import DeviceInfo from 'react-native-device-info';

import RevenueCatService from './services/RevenueCatService';
import { useAICredit } from './store/AICreditContext';
import StickyBottomAdMob from './components/StickyBottomAdMob';

import AICreditBadge from './components/AICreditBadge';
import AICreditModal from './components/AICreditModal';
import DebugOptions from './components/DebugOptions';

// Helper function to format a date
const formatDate = (date) => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

const Setting = () => {
  const {
    isDarkMode,
    toggleTheme,
    shouldShuffleChoices,
    toggleShuffleChoices
  } = usePreferencesContext();
  const { colors } = useTheme();
  const navigation = useNavigation();
  // Get login context
  const loginContext = useLogin();

  // Local state as fallback
  const [localUser, setLocalUser] = useState(null);
  const [localUserProfile, setLocalUserProfile] = useState(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [localIsLoggedIn, setLocalIsLoggedIn] = useState(false);

  // BuyNowModal state
  const [buyNowModalVisible, setBuyNowModalVisible] = useState(false);

  // App version state
  const [appVersion, setAppVersion] = useState('1.0.2'); // Default fallback version

  // Get purchase and subscription status
  const { subscriptionActive, subscriptionInfo, storeSubscriptionStatus, refreshPurchases, isSubscriptionActive, getSubscriptionsInfo, purchases, getPurchases, updateUser, addTestPurchase, logAllPurchases, clearPurchases, clearPurchaseLoadTracking } = usePurchase();

  // Get selected exam information
  const { selectedExam, exams } = useExamContext();

  // Use context values if available, otherwise use local state
  const user = loginContext?.user || localUser;
  const userProfile = loginContext?.userProfile || localUserProfile;
  const isLoggedIn = loginContext?.isLoggedIn || localIsLoggedIn;
  const loading = loginContext?.isLoading || localLoading;
  const [creditModalVisible, setCreditModalVisible] = useState(false);

    const refreshSubscription = useSubscriptionRefresh();

  // Get app version from native side using react-native-device-info
  useEffect(() => {
    const getAppVersion = async () => {
      try {
        const version = DeviceInfo.getVersion();
        setAppVersion(version);
      } catch (error) {
        console.log('Could not get app version, using fallback:', error);
        // Keep the default fallback version
      }
    };

    getAppVersion();

  }, []);

  // Initialize local state from AsyncStorage if LoginContext is not available
  useEffect(() => {
    // Skip if LoginContext is available and initialized
    if (loginContext && (loginContext.isInitialized || loginContext.user)) {
      return;
    }

  }, [loginContext, refreshPurchases]);

  // Optimize theme toggle with useCallback
  const memoizedToggleTheme = useCallback(() => {
    toggleTheme();
  }, [toggleTheme]);

  // Removed SectionHeader component

  // Memoized logout confirmation handler
  const handleLogoutConfirmation = useCallback(() => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Confirm', onPress: handleLogout },
    ]);
  }, []);

  // Memoized user info component for better performance
  const UserInfo = useCallback(() => (
    <List.Item
      title={userProfile?.name}
      description={userProfile?.email}
      left={() => (
        <Image
          source={{ uri: userProfile?.profile_image }}
          style={styles.userAvatar}
        />
      )}
      right={() => (
        <TouchableOpacity
          onPress={handleLogoutConfirmation}
          style={styles.logoutButton}
        >
          <List.Icon icon="logout" />
        </TouchableOpacity>
      )}
      style={styles.listItem}
    />
  ), [userProfile, handleLogoutConfirmation]);

  // Memoized Buy Now handler
  const handleBuyNow = useCallback((product) => {
    console.log('Opening BuyNowModal with product:', product?.exam_code);
    setBuyNowModalVisible(true);
  }, []);

  // Get QnA context for reloading questions
  const { loadQnA, pendingExamCodeRef } = useQnAContext();

  // State for active purchases count
  const [activePurchases, setActivePurchases] = useState([]);


  const handleLogin = async () => {
    try {
      setLocalLoading(true);
      console.log('[LOGINSYNC] Starting login process');

      // Clear tracking to prevent duplicate requests
      if (apiClient?.clearQnARequestTracker) {
        apiClient.clearQnARequestTracker();
      }
      if (clearPurchaseLoadTracking) {
        clearPurchaseLoadTracking();
      }

      // Perform Google login
      const loginResult = await AuthService.loginWithGoogle();

      if (!loginResult?.success || !loginResult?.user) {
        Alert.alert('Sign-In Error', 'Unable to retrieve user data. Please try again.');
        return;
      }

      const userData = loginResult.user;
      const userId = userData.id || userData._id;

      if (!userId) {
        Alert.alert('Sign-In Error', 'Unable to retrieve user ID. Please try again.');
        return;
      }

      // Update local state
      setLocalUser(userData);
      setLocalIsLoggedIn(true);

      const profileData = {
        name: userData.name || '',
        email: userData.email || '',
        profile_image: userData.photo || ''
      };
      setLocalUserProfile(profileData);

      // Store data in AsyncStorage
      await AsyncStorage.setItem('user', JSON.stringify(userData));
      await AsyncStorage.setItem('apires', JSON.stringify(profileData));
      await AsyncStorage.setItem('loginStateChanged', JSON.stringify({
        isLoggedIn: true,
        timestamp: Date.now()
      }));

      // Update LoginContext if available
      if (loginContext?.updateUserData) {
        await loginContext.updateUserData(userData);
      } else if (loginContext?.notifyLoginStateChanged) {
        await loginContext.notifyLoginStateChanged(true);
      }

      // Initialize RevenueCat
      try {
        await RevenueCatService.initialize(userId);
        const customerInfo = await RevenueCatService.getCustomerInfo();
        if (updateUser) {
          await updateUser(userId, { revenueCatCustomerInfo: customerInfo });
        }
      } catch (rcError) {
        console.error('[LOGINSYNC] RevenueCat initialization failed:', rcError);
      }

      console.log('[LOGINSYNC] Login successful, starting post-login flow');

      await getSubscriptionsInfo(selectedExam.exam_code, true);
      //await refreshSubscription();

      console.log('[LOGINSYNC] Login flow completed successfully');

    } catch (error) {
      console.error('Login error:', error);

      // Handle user cancellation gracefully
      if (error?.code === 12501 || error?.message?.includes('cancelled')) {
        console.log('[LOGINSYNC] User cancelled login');
        return;
      }

      // Handle specific error codes
      let errorMessage = 'An unexpected error occurred during sign-in.';
      if (error?.code) {
        switch (error.code) {
          case 7:
            errorMessage = 'Network error. Please check your internet connection.';
            break;
          case 10:
            errorMessage = 'Developer error. Please contact support.';
            break;
          case 12500:
            errorMessage = 'Play Services not available or outdated.';
            break;
          default:
            errorMessage = `Sign-in error (${error.code}). Please try again.`;
        }
      }

      Alert.alert('Sign-In Error', errorMessage);
    } finally {
      setLocalLoading(false);
    }
  };

  /**
   * Handle user logout - uses the centralized logout utility
   */
  const handleLogout = async () => {
    try {
      setLocalLoading(true);

      // Use LoginContext for logout if available
      if (loginContext && loginContext.logout) {
        console.log('[LOGINSYNC] Using LoginContext for logout');
        const success = await loginContext.logout();

        if (success) {
          console.log('[LOGINSYNC] Logout successful via LoginContext');

          // Update local state to reflect logout
          setLocalIsLoggedIn(false);
          setLocalUser(null);
          setLocalUserProfile(null);

          // Clear the Google ID token from AsyncStorage
          try {
            await AsyncStorage.removeItem('google_id_token');
            console.log('[Setting] Cleared Google ID token from AsyncStorage during logout');
          } catch (tokenError) {
            console.error('[Setting] Error clearing Google ID token:', tokenError);
          }

          // Update active purchases state
          //updateActivePurchases();

          // Reload QnA with free content
          if (selectedExam && selectedExam.exam_code && loadQnA) {
            // Add a small delay to ensure logout is fully processed
            await new Promise(resolve => setTimeout(resolve, 300));

            try {
              //await loadQnA(selectedExam.exam_code, true, selectedExam.id);
              await storeSubscriptionStatus(selectedExam.exam_code, false);
              console.log('[LOGINSYNC] QnA reload successful after LoginContext logout');
            } catch (error) {
              console.error('[LOGINSYNC] Error reloading QnA after LoginContext logout:', error);
            }
          }
        } else {
          console.error('[LOGINSYNC] Logout failed via LoginContext');
          Alert.alert('Logout Failed', 'Please try again.');
        }

        setLocalLoading(false);
        return;
      } else {

        // Fallback to AuthService logout if LoginContext is not available
        console.log('[LOGINSYNC] LoginContext not available, using AuthService logout');

        try {
          // Use AuthService for comprehensive logout
          const authLogoutResult = await AuthService.logout();
          console.log('[LOGINSYNC] AuthService logout result:', authLogoutResult);

          if (authLogoutResult.success) {
            console.log('[LOGINSYNC] AuthService logout successful');

            // Clear local state
            setLocalIsLoggedIn(false);
            setLocalUser(null);
            setLocalUserProfile(null);

            // Clear purchases
            if (clearPurchases) {
              clearPurchases();
            }

            // Update active purchases state
            //updateActivePurchases();

            // Reload QnA with free content
            if (selectedExam && selectedExam.exam_code && loadQnA) {
              await new Promise(resolve => setTimeout(resolve, 300));
              try {
                //await loadQnA(selectedExam.exam_code, true, selectedExam.id);
                await storeSubscriptionStatus(selectedExam.exam_code, subscriptionActive);
                console.log('[LOGINSYNC] QnA reload successful after AuthService logout');
              } catch (error) {
                console.error('[LOGINSYNC] Error reloading QnA after AuthService logout:', error);
              }
            }
          } else {
            console.error('[LOGINSYNC] AuthService logout failed');
            Alert.alert('Logout Failed', 'Please try again.');
          }
        } catch (authLogoutError) {
          console.error('[LOGINSYNC] Error during AuthService logout:', authLogoutError);

          // Fallback to centralized logout utility
          console.log('[LOGINSYNC] Falling back to centralized logout utility');
          const success = await performLogout({
            clearPurchases,
            clearCache: apiClient?.clearCache,
            loadQnA,
            currentExam: selectedExam,
            pendingExamCodeRef
          });

          if (success) {
            // Update local state to reflect logout
            setLocalIsLoggedIn(false);
            setLocalUser(null);
            setLocalUserProfile(null);

            // Clear the Google ID token from AsyncStorage
            try {
              await AsyncStorage.removeItem('google_id_token');
              console.log('[Setting] Cleared Google ID token from AsyncStorage during logout');
            } catch (tokenError) {
              console.error('[Setting] Error clearing Google ID token:', tokenError);
            }

            // Update active purchases state
            //updateActivePurchases();

            console.log('[LOGINSYNC] Logout successful via centralized utility');
          } else {
            console.error('[LOGINSYNC] Logout failed via centralized utility');
            Alert.alert('Logout Failed', 'Please try again.');
          }
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Logout Error', error.message);
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Appbar.Header elevated>
        {/* App icon on the left */}
        <View style={{ marginLeft: 10, marginRight: 4 }}>
          <Image
            source={require('../android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png')}
            style={{ width: 28, height: 28 }}
          />
        </View>

        {/* Title for Settings */}
        <Appbar.Content
          title={selectedExam?.exam_code || ""}
          titleStyle={{
            paddingHorizontal: 12,
          }}
        />

        {/* Browse Exams button on the right */}
        <Button
          mode="text"
          onPress={() => navigation.navigate('Welcome2')}
          labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
          compact
          style={{ marginRight: 8 }}
        >
          Browse Exams
        </Button>
        <AICreditBadge onPress={() => setCreditModalVisible(true)} />
      </Appbar.Header>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={true}
      >
        <List.Section>
          {/* 1. Google Login / User Info */}
          {isLoggedIn ? (
            <UserInfo />
          ) : (
            <View style={styles.googleButtonContainer}>
              <GoogleSigninButton
                style={styles.googleButton}
                size={GoogleSigninButton.Size.Wide}
                color={GoogleSigninButton.Color.Dark}
                onPress={handleLogin}
                disabled={loading}
              />
            </View>
          )}
          <Divider />

          {/* 2. Selected Exam Card - Show for all users */}
          {selectedExam ? (
            <View style={[styles.customCard, {
              backgroundColor: colors.surface,
              borderColor: colors.outline,
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4,
            }]}>
              <View style={styles.customCardContent}>
                <View style={styles.accessCardHeader}>
                  <View style={styles.examInfoContainer}>
                    <Text style={[styles.examName, { color: colors.primary }]}>
                      {selectedExam.exam_name}
                    </Text>
                    <Text style={[styles.examCode, { color: colors.onSurfaceVariant }]}>
                      {selectedExam.exam_code}
                    </Text>
                  </View>

                  {/* Access Badge */}
                  {selectedExam ? (
                    <View style={[
                      styles.accessBadge,
                      {
                        backgroundColor: subscriptionActive
                          ? colors.primaryContainer
                          : colors.surfaceVariant
                      }
                    ]}>
                      <Text style={{
                        color: subscriptionActive
                          ? colors.primary
                          : colors.onSurfaceVariant,
                        fontWeight: subscriptionActive ? 'bold' : 'normal',
                        fontSize: 12
                      }}>
                        {subscriptionActive
                          ? 'PREMIUM ACCESS'
                          : 'FREE SAMPLE'}
                      </Text>
                    </View>
                  ) : null}
                </View>


                {/* Subscription Info */}
                {(() => {
                  if (subscriptionInfo && subscriptionInfo.isActive && subscriptionInfo.details?.length > 0) {
                    const detail = subscriptionInfo.details[0]; // Get the first active subscription
                    const purchaseDate = detail.purchaseDate ? new Date(detail.purchaseDate) : null;
                    const expiryDate = detail.expiresDate ? new Date(detail.expiresDate) : null;
                    const daysRemaining = expiryDate ? Math.ceil((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : null;

                    return (
                      <View style={styles.subscriptionInfo}>
                        <Divider style={[styles.divider, { backgroundColor: colors.outline }]} />
                        <View style={styles.infoRow}>
                          <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Subscription:</Text>
                          <Text style={[styles.infoValue, { color: colors.primary }]}>Active</Text>
                        </View>
                        {detail.productId && (
                          <View style={styles.infoRow}>
                            <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Plan:</Text>
                            <Text style={[styles.infoValue, { color: colors.onSurface }]}>
                              {detail.productId.includes('weekly') ? 'Weekly' :
                                detail.productId.includes('monthly') ? 'Monthly' :
                                  detail.productId.includes('yearly') ? 'Yearly' : 'Premium'}
                            </Text>
                          </View>
                        )}
                        {purchaseDate && (
                          <View style={styles.infoRow}>
                            <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Purchased:</Text>
                            <Text style={[styles.infoValue, { color: colors.secondary }]}>
                              {purchaseDate.toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </Text>
                          </View>
                        )}
                        {expiryDate && (
                          <View style={styles.infoRow}>
                            <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Expires:</Text>
                            <Text style={[styles.infoValue, { color: colors.secondary }]}>
                              {expiryDate.toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </Text>
                          </View>
                        )}
                        {daysRemaining !== null && (
                          <View style={styles.infoRow}>
                            <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Remaining:</Text>
                            <Text style={[styles.infoValue, { color: daysRemaining > 7 ? colors.secondary : colors.error }]}>
                              {daysRemaining > 0 ? `${daysRemaining} days` : 'Expired'}
                            </Text>
                          </View>
                        )}
                        {detail.willRenew !== undefined && (
                          <View style={styles.infoRow}>
                            <Text style={[styles.infoLabel, { color: colors.onSurface }]}>Auto-Renew:</Text>
                            <Text style={[styles.infoValue, { color: detail.willRenew ? colors.primary : colors.error }]}>
                              {detail.willRenew ? 'Enabled' : 'Disabled'}
                            </Text>
                          </View>
                        )}
                      </View>
                    );
                  }
                  return null;
                })()}

                {/* Free Sample Info */}
                {(() => {
                  if (selectedExam && !subscriptionActive) {
                    return (
                      <View style={styles.subscriptionInfo}>
                        <Text style={[styles.freeInfo, { color: colors.onSurface }]}>
                          You are currently using the free sample version with limited access to questions.
                        </Text>
                        <Text style={[styles.upgradeInfo, { color: colors.primary }]}>
                          Upgrade to premium for full access to all questions and features.
                        </Text>

                        {/* Add a button to upgrade */}
                        <TouchableOpacity
                          style={[styles.upgradeButton, { backgroundColor: colors.primary }]}
                          onPress={() => handleBuyNow(selectedExam)}
                        >
                          <Text style={[styles.upgradeButtonText, { color: "#ffffff" }]}>Upgrade Now</Text>
                        </TouchableOpacity>
                      </View>
                    );
                  }
                  return null;
                })()}
              </View>
            </View>
          ) : (
            <List.Item
              title="No Exam Selected"
              description="Please select an exam from the home screen"
              left={() => <List.Icon icon="information-outline" color={colors.onSurfaceVariant} />}
              style={styles.listItem}
            />
          )}

          {/* 4. Dark Theme */}
          <List.Item
            title="Dark Theme"
            onPress={memoizedToggleTheme}
            left={() => <List.Icon icon="theme-light-dark" />}
            right={() => (
              <Switch
                value={isDarkMode}
                onValueChange={toggleTheme}
              />
            )}
            style={styles.listItem}
          />
          <Divider />

          {/* 5. Shuffle Choices */}
          <List.Item
            title="Shuffle Choices"
            onPress={toggleShuffleChoices}
            left={() => <List.Icon icon="shuffle-variant" />}
            right={() => (
              <Switch
                value={shouldShuffleChoices}
                onValueChange={toggleShuffleChoices}
              />
            )}
            style={styles.listItem}
          />
          <Divider />

          {/* 6. App Version */}
          <View style={styles.versionContainer}>
            <PaperText
              variant="bodyMedium"
              style={[styles.versionText, { color: colors.onSurfaceVariant }]}
            >
              App Version: {appVersion}
            </PaperText>
          </View>
          <Divider />
          {__DEV__ && <DebugOptions />}
        </List.Section >

      </ScrollView >

      {/* Loading Overlay */}
      < Portal >
        <Modal visible={loading} dismissable={false}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator
              size="large"
              color={colors.primary}
            />
            <Text style={{ color: colors.text, marginTop: 16 }}>
              Processing...
            </Text>
          </View>
        </Modal>
      </Portal >
      {/* BuyNowModal for upgrading */}

      <BuyNowModal
        buyNowModalVisible={buyNowModalVisible}
        setBuyNowModalVisible={setBuyNowModalVisible}
        selectedProduct={selectedExam}
        /* onPurchaseComplete={async (result) => {
          console.log('[Setting] Purchase completed, refreshing subscription status');
          await refreshSubscription();
        }} */
      />
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
      <AICreditModal
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />
    </View >
  );
};

const screenWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 24,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 20,
    marginBottom: 8,
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 16,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoutText: {
    marginRight: 4,
    color: '#F44336',
  },
  card: {
    margin: 16,
    marginTop: 8,
    elevation: 2,
  },
  googleButtonContainer: {
    padding: 16,
    alignItems: 'center',
  },
  googleButton: {
    width: screenWidth - 32,
    height: 48,
  },
  loadingContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 32,
    borderRadius: 12,
    alignItems: 'center',
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingSubscription: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  // Custom card styles to replace Card component
  customCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 4,
  },
  customCardContent: {
    padding: 16,
  },
  // Product card styles (similar to ProductSelect.js)
  productCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  productContent: {
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    marginRight: 8,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  productName: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 4,
  },
  metaContainer: {
    flexDirection: 'column',
    marginTop: 4,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaText: {
    fontSize: 13,
    fontWeight: '500',
  },
  separator: {
    width: 1,
    height: 12,
    opacity: 0.5,
  },
  // Access card styles
  accessCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  examInfoContainer: {
    flex: 1,
    marginRight: 8,
  },
  examName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  examCode: {
    fontSize: 14,
    opacity: 0.7,
  },
  accessBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  divider: {
    marginBottom: 12,
  },
  sectionDivider: {
    marginVertical: 16,
  },
  subscriptionInfo: {
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    fontWeight: '500',
    opacity: 0.8,
  },
  infoValue: {
    fontWeight: '600',
  },
  freeInfo: {
    marginBottom: 8,
    lineHeight: 20,
  },
  upgradeInfo: {
    fontWeight: '600',
    marginTop: 4,
    marginBottom: 12,
  },
  upgradeButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'center',
    marginTop: 8,
  },
  upgradeButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  loginLoadingContainer: {
    height: 48,
    width: screenWidth - 32,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
  },
  loginLoadingText: {
    marginLeft: 12,
    fontWeight: '600',
    fontSize: 16,
  },
  // App version styles
  versionContainer: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  versionText: {
    fontSize: 14,
    opacity: 0.8,
  },
});

export default Setting;